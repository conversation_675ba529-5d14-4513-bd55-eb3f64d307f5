name: mobile
description: "Offline Flutter Task Management Application with SQLite and Biometric Authentication"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # State Management
  provider: ^6.1.2

  # Database & Storage
  sqflite: ^2.3.3+1
  path: ^1.9.0
  shared_preferences: ^2.2.3
  flutter_secure_storage: ^9.2.2

  # Authentication
  local_auth: ^2.2.0
  crypto: ^3.0.3

  # Date & Time
  intl: ^0.19.0

  # JSON & Serialization
  json_annotation: ^4.9.0

  # HTTP & Networking (for future sync features)
  http: ^1.2.2
  dio: ^5.4.3+1

  # File & Path utilities
  path_provider: ^2.1.3

  # Notifications
  flutter_local_notifications: ^17.2.2

  # UI Components
  flutter_staggered_grid_view: ^0.7.0
  flutter_colorpicker: ^1.1.0
  table_calendar: ^3.1.2

  # Charts & Visualization
  fl_chart: ^0.68.0

  # File handling
  file_picker: ^8.0.6
  image_picker: ^1.1.2

  # Utils
  uuid: ^4.4.2
  logger: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.12
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/images/icons/
    - assets/images/illustrations/
    - assets/images/logos/
    - assets/data/

  # Fonts (placeholder for custom fonts)
  # fonts:
  #   - family: CustomFont
  #     fonts:
  #       - asset: assets/fonts/CustomFont-Regular.ttf
  #       - asset: assets/fonts/CustomFont-Bold.ttf
  #         weight: 700
