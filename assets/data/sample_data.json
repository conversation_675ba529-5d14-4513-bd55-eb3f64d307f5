{"sample_spaces": [{"id": "1", "name": "Personal", "description": "Personal tasks and projects", "color": "#2196F3"}, {"id": "2", "name": "Work", "description": "Work-related tasks and projects", "color": "#4CAF50"}], "sample_projects": [{"id": "1", "space_id": "1", "name": "Home Renovation", "description": "Kitchen and bathroom renovation project"}, {"id": "2", "space_id": "2", "name": "Mobile App Development", "description": "Flutter task management app"}], "sample_tasks": [{"id": "1", "project_id": "1", "title": "Plan kitchen layout", "description": "Design the new kitchen layout with measurements", "status": "todo", "priority": "high", "due_date": "2024-02-15"}, {"id": "2", "project_id": "2", "title": "Implement user authentication", "description": "Add biometric and password authentication", "status": "in_progress", "priority": "medium", "due_date": "2024-02-10"}]}