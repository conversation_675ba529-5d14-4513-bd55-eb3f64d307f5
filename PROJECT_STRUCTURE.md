# Flutter Task Management App - File Structure

## Complete Directory Structure

```
lib/
├── main.dart                           # App entry point
├── app/                               # App-level configuration
│   ├── app.dart                       # Main app widget
│   ├── routes/                        # App routing
│   │   ├── app_routes.dart           # Route definitions
│   │   ├── route_generator.dart      # Route generation logic
│   │   └── route_names.dart          # Route name constants
│   ├── themes/                        # App theming
│   │   ├── app_theme.dart            # Main theme configuration
│   │   ├── light_theme.dart          # Light theme
│   │   ├── dark_theme.dart           # Dark theme
│   │   └── theme_colors.dart         # Color constants
│   └── constants/                     # App-wide constants
│       ├── app_constants.dart        # General constants
│       ├── database_constants.dart   # Database-related constants
│       └── storage_keys.dart         # Local storage keys
│
├── core/                              # Core functionality
│   ├── database/                      # Database layer
│   │   ├── database_helper.dart      # SQLite database helper
│   │   ├── database_provider.dart    # Database provider
│   │   ├── migrations/               # Database migrations
│   │   │   ├── migration_v1.dart     # Initial schema
│   │   │   ├── migration_v2.dart     # Schema updates
│   │   │   └── migration_manager.dart # Migration management
│   │   └── schema/                   # Database schema definitions
│   │       ├── tables.dart           # Table definitions
│   │       ├── indexes.dart          # Index definitions
│   │       └── constraints.dart      # Constraint definitions
│   │
│   ├── services/                      # Core services
│   │   ├── auth/                     # Authentication services
│   │   │   ├── biometric_service.dart # Biometric authentication
│   │   │   ├── auth_service.dart     # General auth service
│   │   │   └── secure_storage_service.dart # Secure storage
│   │   ├── storage/                  # Storage services
│   │   │   ├── local_storage_service.dart # Local storage
│   │   │   ├── database_service.dart # Database operations
│   │   │   └── backup_service.dart   # Data backup/restore
│   │   ├── sync/                     # Sync services (future)
│   │   │   ├── sync_service.dart     # Data synchronization
│   │   │   └── conflict_resolver.dart # Sync conflict resolution
│   │   └── notification/             # Notification services
│   │       ├── notification_service.dart # Local notifications
│   │       └── reminder_service.dart # Task reminders
│   │
│   ├── utils/                         # Utility classes
│   │   ├── date_utils.dart           # Date/time utilities
│   │   ├── validation_utils.dart     # Input validation
│   │   ├── format_utils.dart         # Data formatting
│   │   ├── export_utils.dart         # Data export utilities
│   │   └── logger.dart               # Logging utility
│   │
│   ├── errors/                        # Error handling
│   │   ├── exceptions.dart           # Custom exceptions
│   │   ├── error_handler.dart        # Global error handler
│   │   └── failure.dart              # Failure classes
│   │
│   └── extensions/                    # Dart extensions
│       ├── string_extensions.dart    # String extensions
│       ├── datetime_extensions.dart  # DateTime extensions
│       └── list_extensions.dart      # List extensions
│
├── data/                              # Data layer
│   ├── models/                        # Data models
│   │   ├── user/                     # User-related models
│   │   │   ├── user_model.dart       # User model
│   │   │   ├── user_profile_model.dart # User profile
│   │   │   └── user_settings_model.dart # User settings
│   │   ├── space/                    # Space models
│   │   │   ├── space_model.dart      # Space model
│   │   │   └── space_member_model.dart # Space membership
│   │   ├── project/                  # Project models
│   │   │   ├── project_model.dart    # Project model
│   │   │   └── project_member_model.dart # Project membership
│   │   ├── task/                     # Task models
│   │   │   ├── task_model.dart       # Task model
│   │   │   ├── task_status_model.dart # Task status
│   │   │   ├── task_priority_model.dart # Task priority
│   │   │   ├── task_attachment_model.dart # Task attachments
│   │   │   └── task_comment_model.dart # Task comments
│   │   ├── category/                 # Category models
│   │   │   └── category_model.dart   # Category model
│   │   ├── custom_field/             # Custom field models
│   │   │   ├── custom_field_model.dart # Custom field definition
│   │   │   ├── custom_field_value_model.dart # Custom field values
│   │   │   └── field_type_model.dart # Field type definitions
│   │   └── common/                   # Common models
│   │       ├── base_model.dart       # Base model class
│   │       ├── audit_model.dart      # Audit trail model
│   │       └── metadata_model.dart   # Metadata model
│   │
│   ├── repositories/                  # Repository pattern implementation
│   │   ├── base/                     # Base repository
│   │   │   ├── base_repository.dart  # Base repository interface
│   │   │   └── repository_impl.dart  # Base repository implementation
│   │   ├── user_repository.dart      # User data repository
│   │   ├── space_repository.dart     # Space data repository
│   │   ├── project_repository.dart   # Project data repository
│   │   ├── task_repository.dart      # Task data repository
│   │   ├── category_repository.dart  # Category data repository
│   │   └── custom_field_repository.dart # Custom field repository
│   │
│   └── datasources/                   # Data sources
│       ├── local/                    # Local data sources
│       │   ├── user_local_datasource.dart # User local data
│       │   ├── space_local_datasource.dart # Space local data
│       │   ├── project_local_datasource.dart # Project local data
│       │   ├── task_local_datasource.dart # Task local data
│       │   ├── category_local_datasource.dart # Category local data
│       │   └── custom_field_local_datasource.dart # Custom field local data
│       └── remote/                   # Remote data sources (future)
│           ├── user_remote_datasource.dart # User remote data
│           ├── space_remote_datasource.dart # Space remote data
│           ├── project_remote_datasource.dart # Project remote data
│           ├── task_remote_datasource.dart # Task remote data
│           ├── category_remote_datasource.dart # Category remote data
│           └── custom_field_remote_datasource.dart # Custom field remote data
│
├── presentation/                      # Presentation layer
│   ├── providers/                     # State management (Provider)
│   │   ├── auth/                     # Authentication providers
│   │   │   ├── auth_provider.dart    # Authentication state
│   │   │   └── biometric_provider.dart # Biometric auth state
│   │   ├── user/                     # User providers
│   │   │   ├── user_provider.dart    # User state
│   │   │   └── user_profile_provider.dart # User profile state
│   │   ├── space/                    # Space providers
│   │   │   └── space_provider.dart   # Space state management
│   │   ├── project/                  # Project providers
│   │   │   └── project_provider.dart # Project state management
│   │   ├── task/                     # Task providers
│   │   │   ├── task_provider.dart    # Task state management
│   │   │   ├── task_filter_provider.dart # Task filtering
│   │   │   └── task_view_provider.dart # Task view state
│   │   ├── category/                 # Category providers
│   │   │   └── category_provider.dart # Category state management
│   │   ├── custom_field/             # Custom field providers
│   │   │   └── custom_field_provider.dart # Custom field state
│   │   └── common/                   # Common providers
│   │       ├── theme_provider.dart   # Theme state
│   │       ├── loading_provider.dart # Loading state
│   │       └── navigation_provider.dart # Navigation state
│   │
│   ├── screens/                       # App screens
│   │   ├── auth/                     # Authentication screens
│   │   │   ├── login_screen.dart     # Login screen
│   │   │   ├── biometric_setup_screen.dart # Biometric setup
│   │   │   └── auth_wrapper_screen.dart # Auth wrapper
│   │   ├── onboarding/               # Onboarding screens
│   │   │   ├── welcome_screen.dart   # Welcome screen
│   │   │   ├── tutorial_screen.dart  # App tutorial
│   │   │   └── profile_setup_screen.dart # Initial profile setup
│   │   ├── home/                     # Home screens
│   │   │   ├── home_screen.dart      # Main home screen
│   │   │   └── dashboard_screen.dart # Dashboard overview
│   │   ├── spaces/                   # Space management screens
│   │   │   ├── spaces_list_screen.dart # List of spaces
│   │   │   ├── space_detail_screen.dart # Space details
│   │   │   ├── create_space_screen.dart # Create new space
│   │   │   └── edit_space_screen.dart # Edit space
│   │   ├── projects/                 # Project management screens
│   │   │   ├── projects_list_screen.dart # List of projects
│   │   │   ├── project_detail_screen.dart # Project details
│   │   │   ├── create_project_screen.dart # Create new project
│   │   │   └── edit_project_screen.dart # Edit project
│   │   ├── tasks/                    # Task management screens
│   │   │   ├── task_list_screen.dart # Task list view
│   │   │   ├── task_detail_screen.dart # Task details
│   │   │   ├── create_task_screen.dart # Create new task
│   │   │   ├── edit_task_screen.dart # Edit task
│   │   │   └── task_search_screen.dart # Task search
│   │   ├── views/                    # Different task views
│   │   │   ├── table_view_screen.dart # Table view
│   │   │   ├── kanban_view_screen.dart # Kanban board view
│   │   │   ├── timeline_view_screen.dart # Timeline view
│   │   │   └── calendar_view_screen.dart # Calendar view
│   │   ├── categories/               # Category management screens
│   │   │   ├── categories_screen.dart # Category management
│   │   │   ├── create_category_screen.dart # Create category
│   │   │   └── edit_category_screen.dart # Edit category
│   │   ├── custom_fields/            # Custom field management screens
│   │   │   ├── custom_fields_screen.dart # Custom field management
│   │   │   ├── create_custom_field_screen.dart # Create custom field
│   │   │   └── edit_custom_field_screen.dart # Edit custom field
│   │   ├── profile/                  # User profile screens
│   │   │   ├── profile_screen.dart   # User profile
│   │   │   ├── edit_profile_screen.dart # Edit profile
│   │   │   └── settings_screen.dart  # App settings
│   │   └── common/                   # Common screens
│   │       ├── splash_screen.dart    # Splash screen
│   │       ├── error_screen.dart     # Error screen
│   │       └── no_internet_screen.dart # No internet screen
│   │
│   └── widgets/                       # Reusable widgets
│       ├── common/                   # Common widgets
│       │   ├── custom_app_bar.dart   # Custom app bar
│       │   ├── custom_button.dart    # Custom button
│       │   ├── custom_text_field.dart # Custom text field
│       │   ├── loading_widget.dart   # Loading indicator
│       │   ├── empty_state_widget.dart # Empty state
│       │   ├── error_widget.dart     # Error display
│       │   └── confirmation_dialog.dart # Confirmation dialog
│       ├── auth/                     # Authentication widgets
│       │   ├── biometric_button.dart # Biometric auth button
│       │   └── auth_form.dart        # Authentication form
│       ├── space/                    # Space-related widgets
│       │   ├── space_card.dart       # Space card widget
│       │   ├── space_list_item.dart  # Space list item
│       │   └── space_selector.dart   # Space selector dropdown
│       ├── project/                  # Project-related widgets
│       │   ├── project_card.dart     # Project card widget
│       │   ├── project_list_item.dart # Project list item
│       │   └── project_progress_bar.dart # Project progress
│       ├── task/                     # Task-related widgets
│       │   ├── task_card.dart        # Task card widget
│       │   ├── task_list_item.dart   # Task list item
│       │   ├── task_priority_badge.dart # Priority badge
│       │   ├── task_status_chip.dart # Status chip
│       │   ├── task_due_date_chip.dart # Due date chip
│       │   └── task_assignee_avatar.dart # Assignee avatar
│       ├── views/                    # View-specific widgets
│       │   ├── table/                # Table view widgets
│       │   │   ├── task_table.dart   # Task table
│       │   │   ├── table_header.dart # Table header
│       │   │   └── table_row.dart    # Table row
│       │   ├── kanban/               # Kanban view widgets
│       │   │   ├── kanban_board.dart # Kanban board
│       │   │   ├── kanban_column.dart # Kanban column
│       │   │   └── kanban_card.dart  # Kanban card
│       │   ├── timeline/             # Timeline view widgets
│       │   │   ├── timeline_view.dart # Timeline view
│       │   │   ├── timeline_item.dart # Timeline item
│       │   │   └── timeline_marker.dart # Timeline marker
│       │   └── calendar/             # Calendar view widgets
│       │       ├── calendar_view.dart # Calendar view
│       │       ├── calendar_day.dart # Calendar day
│       │       └── calendar_event.dart # Calendar event
│       ├── category/                 # Category-related widgets
│       │   ├── category_chip.dart    # Category chip
│       │   ├── category_selector.dart # Category selector
│       │   └── category_color_picker.dart # Color picker
│       ├── custom_field/             # Custom field widgets
│       │   ├── custom_field_input.dart # Custom field input
│       │   ├── field_type_selector.dart # Field type selector
│       │   └── dynamic_form_field.dart # Dynamic form field
│       └── forms/                    # Form widgets
│           ├── form_builder.dart     # Dynamic form builder
│           ├── form_validator.dart   # Form validation
│           └── form_field_wrapper.dart # Form field wrapper
│
└── assets/                            # App assets
    ├── images/                        # Image assets
    │   ├── icons/                    # Custom icons
    │   ├── illustrations/            # Illustrations
    │   └── logos/                    # App logos
    ├── fonts/                         # Custom fonts
    └── data/                          # Static data files
        ├── sample_data.json          # Sample data for development
        └── field_types.json          # Custom field type definitions
```

## Database Schema Files

```
lib/core/database/schema/
├── tables.dart                       # All table definitions
├── indexes.dart                      # Database indexes
├── constraints.dart                  # Foreign key constraints
└── views.dart                        # Database views (optional)
```

## Configuration Files

```
├── pubspec.yaml                      # Dependencies and assets
├── analysis_options.yaml            # Dart analysis options
├── android/app/build.gradle         # Android configuration
├── ios/Runner/Info.plist            # iOS configuration
└── lib/app/config/                   # App configuration
    ├── app_config.dart              # App configuration
    ├── database_config.dart         # Database configuration
    └── security_config.dart         # Security configuration
```

This structure provides:

1. **Scalable Architecture**: Clear separation of concerns with data, presentation, and core layers
2. **Offline-First**: SQLite database with proper schema management and migrations
3. **State Management**: Provider pattern for reactive state management
4. **Multiple Views**: Dedicated widgets and screens for table, kanban, timeline, and calendar views
5. **Biometric Auth**: Dedicated authentication services and providers
6. **Custom Fields**: Dynamic custom field system with proper models and widgets
7. **Repository Pattern**: Clean data access layer with local/remote data sources
8. **Maintainable**: Organized by feature with reusable components
9. **Extensible**: Easy to add new features and views

The structure supports the complete data hierarchy (Spaces → Projects → Tasks → Categories) with proper relationships and custom field support.
